import * as React from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";

interface SeoOptimizationHeaderProps {
  totalProducts: number;
  selectedCount: number;
  averageSeoScore: number;
  onOptimizeSelected: () => void;
  onSelectAll: () => void;
  isOptimizing: boolean;
}

export function SeoOptimizationHeader({ 
  totalProducts, 
  selectedCount, 
  averageSeoScore, 
  onOptimizeSelected, 
  onSelectAll,
  isOptimizing 
}: SeoOptimizationHeaderProps) {
  return (
    <div className="relative bg-black text-white py-24 px-6 overflow-hidden">
      {/* Dynamic Product Grid Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="grid grid-cols-10 gap-1 h-full">
          {Array.from({ length: 100 }).map((_, i) => (
            <motion.div
              key={i}
              className="bg-white rounded-sm"
              initial={{ opacity: 0.1 }}
              animate={{ 
                opacity: [0.1, 0.5, 0.1],
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: (i % 10) * 0.2,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </div>

      {/* Floating SEO Elements */}
      <div className="absolute inset-0">
        {[
          { text: "TITLE", x: "10%", y: "15%" },
          { text: "DESC", x: "85%", y: "20%" },
          { text: "TAGS", x: "15%", y: "75%" },
          { text: "ALT", x: "90%", y: "80%" },
          { text: "H1", x: "25%", y: "40%" },
          { text: "META", x: "75%", y: "45%" }
        ].map((item, i) => (
          <motion.div
            key={item.text}
            className="absolute text-xs font-bold bg-white/10 rounded-full px-3 py-1"
            style={{ left: item.x, top: item.y }}
            animate={{
              opacity: [0.3, 0.7, 0.3],
              scale: [0.9, 1.1, 0.9],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 0.4,
              ease: "easeInOut"
            }}
          >
            {item.text}
          </motion.div>
        ))}
      </div>

      <div className="max-w-6xl mx-auto relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
          {/* Left - Title & Description */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <div className="inline-flex items-center bg-white/10 rounded-full px-4 py-2 mb-6">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-2 animate-pulse" />
                <span className="text-sm font-medium">BULK OPTIMIZATION</span>
                <div className="w-2 h-2 bg-green-400 rounded-full ml-2 animate-pulse" />
              </div>
              
              <h1
                style={{
                  fontSize: 'clamp(4rem, 8vw, 8rem)',
                  fontWeight: 900,
                  lineHeight: 0.8,
                  letterSpacing: '-0.05em',
                  color: 'white',
                  marginBottom: '3rem'
                }}
              >
                Product
                <br />
                <span style={{ color: '#9CA3AF' }}>Optimizer</span>
              </h1>

              <p
                style={{
                  fontSize: '1.25rem',
                  fontWeight: 500,
                  color: '#D1D5DB',
                  lineHeight: 1.6,
                  maxWidth: '48rem',
                  marginBottom: '2rem'
                }}
              >
                AI-powered bulk SEO optimization for your entire product catalog.
                Select, optimize, and dominate search results.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
              className="flex flex-wrap gap-4 mt-8"
            >
              <Button
                onClick={onSelectAll}
                size="lg"
                className="bg-white/10 text-white border-2 border-white hover:bg-white hover:text-black"
              >
                Select All ({totalProducts})
              </Button>
              
              <Button
                onClick={onOptimizeSelected}
                disabled={selectedCount === 0 || isOptimizing}
                size="lg"
                className="bg-white text-black hover:bg-gray-100 disabled:opacity-50"
              >
                {isOptimizing ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
                    <span>Optimizing...</span>
                  </div>
                ) : (
                  `Optimize ${selectedCount} Selected`
                )}
              </Button>
            </motion.div>
          </div>

          {/* Right - Live Stats */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            className="lg:text-right"
          >
            <div className="bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10">
              <div className="space-y-6">
                <div>
                  <div className="text-3xl font-black mb-1">{selectedCount}</div>
                  <div className="text-gray-300 text-sm">Selected Products</div>
                </div>
                
                <div>
                  <div className="text-3xl font-black mb-1">{averageSeoScore}</div>
                  <div className="text-gray-300 text-sm">Average SEO Score</div>
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-300 text-sm">Catalog Health</span>
                    <span className="font-bold text-sm">{Math.round((averageSeoScore / 100) * 100)}%</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <motion.div
                      className="bg-white rounded-full h-2"
                      initial={{ width: 0 }}
                      animate={{ width: `${averageSeoScore}%` }}
                      transition={{ duration: 1.5, delay: 0.5, ease: "easeOut" }}
                    />
                  </div>
                </div>
                
                <div className="pt-4 border-t border-white/20">
                  <div className="text-xs text-gray-400">
                    Ready to optimize {totalProducts} products
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
